spring.application.name=backendwork
server.port=39010

# ä¸»æ°æ®æºéç½® (é»è®¤æ°æ®æºï¼ç¨äºå­å¨ç½é¡µéç½®)
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.strict=true

# é»è®¤æ°æ®æº (H2åå­æ°æ®åºï¼ç¨äºæ¼ç¤º)
spring.datasource.dynamic.datasource.master.url=jdbc:h2:mem:testdb
spring.datasource.dynamic.datasource.master.driver-class-name=org.h2.Driver
spring.datasource.dynamic.datasource.master.username=sa
spring.datasource.dynamic.datasource.master.password=

# ptfsdev1 datasource
spring.datasource.dynamic.datasource.ptfsdev1.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsdev1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsdev1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.dynamic.datasource.ptfsdev1.username=DENG_HUI
spring.datasource.dynamic.datasource.ptfsdev1.password=Oracl3@1234##
spring.datasource.dynamic.datasource.ptfsdev1.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.dynamic.datasource.ptfsdev1.hikari.connection-init-sql=ALTER SESSION SET CURRENT_SCHEMA=recsys

# PTCCPRO datasource
spring.datasource.dynamic.datasource.ptccpro.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptccpro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.dynamic.datasource.ptccpro.username=CDG_DENG_HUI
spring.datasource.dynamic.datasource.ptccpro.password=Cdg0000278_01
spring.datasource.dynamic.datasource.ptccpro.driver-class-name=oracle.jdbc.OracleDriver

# PTFSPro datasource
spring.datasource.dynamic.datasource.ptfspro.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.dynamic.datasource.ptfspro.username=CDG_DENG_HUI
spring.datasource.dynamic.datasource.ptfspro.password=Cdg0000278_02
spring.datasource.dynamic.datasource.ptfspro.driver-class-name=oracle.jdbc.OracleDriver

# MyBatis Plus configuration
mybatis-plus.mapper-locations=classpath:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.cdgtaxi.entity
mybatis-plus.global-config.db-config.id-type=auto
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.auto-mapping-behavior=full
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql

logging.level.com.dh=debug
logging.level.com.baomidou.dynamic=debug