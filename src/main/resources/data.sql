-- 插入示例网页数据 (如果不存在)
INSERT INTO web_page (title, type, url, query_sql, query_params, description, sort, enabled)
SELECT '百度搜索', 'link', 'https://www.baidu.com', NULL, NULL, '百度搜索引擎', 1, 1
WHERE NOT EXISTS (SELECT 1 FROM web_page WHERE title = '百度搜索');

INSERT INTO web_page (title, type, url, query_sql, query_params, description, sort, enabled)
SELECT 'GitHub', 'link', 'https://github.com', NULL, NULL, '代码托管平台', 2, 1
WHERE NOT EXISTS (SELECT 1 FROM web_page WHERE title = 'GitHub');

INSERT INTO web_page (title, type, url, query_sql, query_params, description, sort, enabled)
SELECT '用户信息查询', 'query', NULL, 'SELECT * FROM users WHERE name LIKE :name', '[{"name":"name","label":"用户名","type":"text"}]', '查询用户信息', 3, 1
WHERE NOT EXISTS (SELECT 1 FROM web_page WHERE title = '用户信息查询');