package com.dh.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("web_page")
public class WebPage {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 页面标题
     */
    private String title;
    
    /**
     * 页面类型 (link: 外部链接, query: 数据库查询页面)
     */
    private String type;
    
    /**
     * 外部链接URL (仅当type为link时使用)
     */
    private String url;
    
    /**
     * 查询SQL (仅当type为query时使用)
     */
    private String querySql;
    
    /**
     * 查询参数定义 (JSON格式)
     */
    private String queryParams;
    
    /**
     * 描述信息
     */
    private String description;
    
    /**
     * 排序字段
     */
    private Integer sort;
    
    /**
     * 是否启用 (0: 禁用, 1: 启用)
     */
    private Integer enabled;

    /**
     * 环境标识 (prod: 生产环境, uat: 测试环境)
     */
    private String env;
}