package com.dh.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.entity.WebPage;
import com.dh.mapper.WebPageMapper;
import com.dh.service.WebPageService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WebPageServiceImpl extends ServiceImpl<WebPageMapper, WebPage> implements WebPageService {
    
    @Autowired
    private WebPageMapper webPageMapper;
    
    @Autowired
    private DataSource dataSource;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public List<WebPage> getAllEnabledWebPages() {
        return webPageMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<WebPage>()
                .eq("enabled", 1)
                .orderByAsc("sort")
        );
    }
    
    @Override
    public List<WebPage> getWebPagesByType(String type) {
        return webPageMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<WebPage>()
                .eq("enabled", 1)
                .eq("type", type)
                .orderByAsc("sort")
        );
    }
    
    @Override
    @DS("ptfsdev1")
    public List<Map<String, Object>> executeQuery(Long webPageId, Map<String, Object> params) {
        WebPage webPage = webPageMapper.selectById(webPageId);
        if (webPage == null || !"query".equals(webPage.getType())) {
            throw new RuntimeException("无效的查询页面");
        }
        
        String sql = webPage.getQuerySql();
        if (sql == null || sql.isEmpty()) {
            throw new RuntimeException("查询SQL为空");
        }
        
        // 替换参数占位符
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof String) {
                sql = sql.replace(":" + key, "'" + value + "'");
            } else {
                sql = sql.replace(":" + key, value.toString());
            }
        }
        
        log.debug("Executing SQL: {}", sql);
        
        try {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            log.error("执行查询失败", e);
            throw new RuntimeException("执行查询失败: " + e.getMessage());
        }
    }
}