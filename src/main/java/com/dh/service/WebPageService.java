package com.dh.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.entity.WebPage;

import java.util.List;
import java.util.Map;

public interface WebPageService extends IService<WebPage> {
    
    /**
     * 获取所有启用的网页列表
     * @return 网页列表
     */
    List<WebPage> getAllEnabledWebPages();
    
    /**
     * 根据类型获取网页列表
     * @param type 网页类型 (link/query)
     * @return 网页列表
     */
    List<WebPage> getWebPagesByType(String type);
    
    /**
     * 执行数据库查询
     * @param webPageId 网页ID
     * @param params 查询参数
     * @return 查询结果
     */
    List<Map<String, Object>> executeQuery(Long webPageId, Map<String, Object> params);
}