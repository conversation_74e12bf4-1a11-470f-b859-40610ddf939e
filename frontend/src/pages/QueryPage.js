import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Table } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import { getWebPageById, executeQuery } from '../services/api';

const QueryPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [webPage, setWebPage] = useState(null);
  const [params, setParams] = useState({});
  const [queryResult, setQueryResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [executing, setExecuting] = useState(false);

  useEffect(() => {
    loadWebPage();
  }, [id]);

  const loadWebPage = async () => {
    try {
      setLoading(true);
      const response = await getWebPageById(id);
      if (response.data.success) {
        const page = response.data.data;
        setWebPage(page);
        
        // 初始化参数
        if (page.queryParams) {
          try {
            const parsedParams = JSON.parse(page.queryParams);
            const initialParams = {};
            parsedParams.forEach(param => {
              initialParams[param.name] = '';
            });
            setParams(initialParams);
          } catch (e) {
            console.error('Error parsing query params:', e);
          }
        }
      }
    } catch (error) {
      console.error('Error loading web page:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleParamChange = (name, value) => {
    setParams({
      ...params,
      [name]: value
    });
  };

  const handleExecuteQuery = async () => {
    try {
      setExecuting(true);
      const response = await executeQuery(id, params);
      if (response.data.success) {
        setQueryResult(response.data.data);
      } else {
        alert('查询失败: ' + response.data.message);
      }
    } catch (error) {
      console.error('Error executing query:', error);
      alert('执行查询时发生错误');
    } finally {
      setExecuting(false);
    }
  };

  if (loading) {
    return (
      <Container className="main-content">
        <p>加载中...</p>
      </Container>
    );
  }

  if (!webPage) {
    return (
      <Container className="main-content">
        <p>未找到网页</p>
      </Container>
    );
  }

  return (
    <Container className="main-content">
      <Row>
        <Col>
          <div className="page-header d-flex justify-content-between align-items-center">
            <h1 className="page-title">查询页面: {webPage.title}</h1>
            <Button variant="secondary" onClick={() => navigate('/')}>
              返回
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card>
            <Card.Header>查询条件</Card.Header>
            <Card.Body>
              {webPage.queryParams ? (
                <>
                  {(() => {
                    try {
                      const parsedParams = JSON.parse(webPage.queryParams);
                      return (
                        <Form>
                          {parsedParams.map((param, index) => (
                            <Form.Group className="mb-3" key={index}>
                              <Form.Label>{param.label}</Form.Label>
                              {param.type === 'select' && param.options ? (
                                <Form.Select
                                  value={params[param.name] || ''}
                                  onChange={(e) => handleParamChange(param.name, e.target.value)}
                                >
                                  <option value="">请选择</option>
                                  {param.options.map((option, optIndex) => (
                                    <option key={optIndex} value={option}>
                                      {option}
                                    </option>
                                  ))}
                                </Form.Select>
                              ) : (
                                <Form.Control
                                  type={param.type || 'text'}
                                  value={params[param.name] || ''}
                                  onChange={(e) => handleParamChange(param.name, e.target.value)}
                                  placeholder={param.placeholder || ''}
                                />
                              )}
                            </Form.Group>
                          ))}
                          <Button 
                            variant="primary" 
                            onClick={handleExecuteQuery} 
                            disabled={executing}
                          >
                            {executing ? '查询中...' : '执行查询'}
                          </Button>
                        </Form>
                      );
                    } catch (e) {
                      return <p>查询参数格式错误</p>;
                    }
                  })()}
                </>
              ) : (
                <p>该查询页面没有定义参数</p>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {queryResult && (
        <Row>
          <Col>
            <Card>
              <Card.Header>查询结果</Card.Header>
              <Card.Body>
                {queryResult.length === 0 ? (
                  <p>查询结果为空</p>
                ) : (
                  <div className="table-responsive">
                    <Table striped bordered hover>
                      <thead>
                        <tr>
                          {Object.keys(queryResult[0]).map((key) => (
                            <th key={key}>{key}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {queryResult.map((row, index) => (
                          <tr key={index}>
                            {Object.values(row).map((value, idx) => (
                              <td key={idx}>{value !== null ? value.toString() : ''}</td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}
    </Container>
  );
};

export default QueryPage;