import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, ListGroup, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { getAllWebPages } from '../services/api';

const GroupedView = () => {
  const [webPages, setWebPages] = useState([]);
  const [groupedPages, setGroupedPages] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Define group order mapping
  const groupOrder = {
    'JIRA': 0,
    'Confluence': 1,
    'Search': 2,
    'Bitbucket': 3,
    'Web Portal': 4
  };

  useEffect(() => {
    loadWebPages();
  }, []);

  const loadWebPages = async () => {
    try {
      setLoading(true);
      const response = await getAllWebPages();
      if (response.data.success) {
        const pages = response.data.data;
        setWebPages(pages);
        groupPagesByGroup(pages);
      } else {
        setError('Failed to load web pages');
      }
    } catch (error) {
      console.error('Error loading web pages:', error);
      setError('Error loading web pages: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const groupPagesByGroup = (pages) => {
    const grouped = {};
    
    pages.forEach(page => {
      const groupName = page.groupName || 'Other';
      if (!grouped[groupName]) {
        grouped[groupName] = [];
      }
      grouped[groupName].push(page);
    });

    // Sort pages within each group by groupId
    Object.keys(grouped).forEach(groupName => {
      grouped[groupName].sort((a, b) => {
        const aId = a.groupId || '';
        const bId = b.groupId || '';
        return aId.localeCompare(bId);
      });
    });

    setGroupedPages(grouped);
  };

  const handlePageClick = (page) => {
    if (page.type === 'link' && page.url) {
      // Open external link in new tab
      window.open(page.url, '_blank', 'noopener,noreferrer');
    } else if (page.type === 'query') {
      // For query pages, you might want to navigate to a query execution page
      // For now, we'll just show an alert
      alert(`Query page: ${page.title}\nThis would open a query interface.`);
    }
  };

  const getSortedGroupNames = () => {
    return Object.keys(groupedPages).sort((a, b) => {
      const orderA = groupOrder[a] !== undefined ? groupOrder[a] : 999;
      const orderB = groupOrder[b] !== undefined ? groupOrder[b] : 999;
      return orderA - orderB;
    });
  };

  const getGroupBadgeVariant = (groupName) => {
    switch (groupName) {
      case 'JIRA':
        return 'primary';
      case 'Confluence':
        return 'success';
      case 'Search':
        return 'info';
      case 'Bitbucket':
        return 'warning';
      case 'Web Portal':
        return 'dark';
      default:
        return 'secondary';
    }
  };

  if (loading) {
    return (
      <Container className="main-content">
        <Row>
          <Col>
            <div className="text-center">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">Loading...</span>
              </Spinner>
              <p className="mt-2">Loading web pages...</p>
            </div>
          </Col>
        </Row>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="main-content">
        <Row>
          <Col>
            <Alert variant="danger">
              {error}
            </Alert>
          </Col>
        </Row>
      </Container>
    );
  }

  return (
    <Container className="main-content">
      <Row>
        <Col>
          <div className="page-header d-flex justify-content-between align-items-center">
            <div>
              <h1 className="page-title">网页分组视图</h1>
              <p className="text-muted mb-0">按分组查看所有网页，点击标题打开链接</p>
            </div>
            <Link to="/">
              <Button variant="outline-secondary">返回管理页面</Button>
            </Link>
          </div>
        </Col>
      </Row>

      <Row>
        <Col>
          {getSortedGroupNames().map(groupName => (
            <Card key={groupName} className="mb-4">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">
                  <Badge bg={getGroupBadgeVariant(groupName)} className="me-2">
                    {groupName}
                  </Badge>
                  ({groupedPages[groupName].length} 项)
                </h5>
              </Card.Header>
              <Card.Body className="p-0">
                <ListGroup variant="flush">
                  {groupedPages[groupName].map(page => (
                    <ListGroup.Item 
                      key={page.id}
                      action
                      onClick={() => handlePageClick(page)}
                      className="d-flex justify-content-between align-items-center"
                      style={{ cursor: 'pointer' }}
                    >
                      <div>
                        <strong>{page.title}</strong>
                        {page.description && (
                          <div className="text-muted small">{page.description}</div>
                        )}
                      </div>
                      <div className="text-end">
                        <Badge bg="light" text="dark" className="me-2">
                          {page.groupId}
                        </Badge>
                        <Badge bg={page.type === 'link' ? 'info' : 'warning'}>
                          {page.type === 'link' ? '链接' : '查询'}
                        </Badge>
                      </div>
                    </ListGroup.Item>
                  ))}
                </ListGroup>
              </Card.Body>
            </Card>
          ))}
          
          {Object.keys(groupedPages).length === 0 && (
            <Card>
              <Card.Body className="text-center">
                <p className="text-muted">暂无网页数据</p>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default GroupedView;
