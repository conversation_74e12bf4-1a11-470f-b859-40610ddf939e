import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import HomePage from './pages/HomePage';
import WebPageForm from './pages/WebPageForm';
import QueryPage from './pages/QueryPage';
import GroupedView from './pages/GroupedView';
import './styles/App.css';

function App() {
  return (
    <Router>
      <div className="app-container">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/grouped" element={<GroupedView />} />
          <Route path="/webpage/new" element={<WebPageForm />} />
          <Route path="/webpage/:id" element={<WebPageForm />} />
          <Route path="/query/:id" element={<QueryPage />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;