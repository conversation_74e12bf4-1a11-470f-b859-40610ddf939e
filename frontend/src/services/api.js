import axios from 'axios';

const API_BASE_URL = 'http://localhost:8080/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 获取所有网页
export const getAllWebPages = () => {
  return api.get('/webpages');
};

// 根据类型获取网页
export const getWebPagesByType = (type) => {
  return api.get(`/webpages/type/${type}`);
};

// 获取单个网页
export const getWebPageById = (id) => {
  return api.get(`/webpages/${id}`);
};

// 创建网页
export const createWebPage = (webPage) => {
  return api.post('/webpages', webPage);
};

// 更新网页
export const updateWebPage = (id, webPage) => {
  return api.put(`/webpages/${id}`, webPage);
};

// 删除网页
export const deleteWebPage = (id) => {
  return api.delete(`/webpages/${id}`);
};

// 执行查询
export const executeQuery = (id, params) => {
  return api.post(`/webpages/${id}/query`, params);
};

export default api;